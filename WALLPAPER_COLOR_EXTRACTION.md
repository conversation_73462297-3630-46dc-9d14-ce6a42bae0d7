# Automatische Farbextraktion aus dem Hintergrundbild

## Übersicht

Diese Implementierung ermöglicht es der DetoxLauncher App, automatisch die hellsten 2 unterschiedlichen Farben aus dem aktuellen Hintergrundbild zu extrahieren und als Primär- und Sekundärfarbe zu verwenden.

## Funktionen

### ✅ Automatische Farbextraktion
- Extrahiert die 2 hellsten und unterschiedlichsten Farben aus dem Hintergrundbild
- Verwendet intelligente Farbquantisierung für bessere Performance
- Berücksichtigt Farbhelligkeit und -unterschiede für optimale Ergebnisse

### ✅ Keine besonderen Berechtigungen erforderlich
- Nutzt `WallpaperManager.getInstance().drawable` - keine zusätzlichen Permissions nötig
- Funktioniert mit dem Standard-Android-Hintergrundbild-System

### ✅ Performance-optimiert
- Skaliert Bilder auf 50x50 Pixel für schnelle Verarbeitung
- Verwendet Coroutines für Hintergrundverarbeitung
- Intelligente Farbquantisierung reduziert ähnliche Farben

### ✅ Benutzerfreundliche UI
- Ein-/Ausschalten über einfachen Schalter in den Einstellungen
- "Farben aktualisieren" Button für manuelle Aktualisierung
- Farbvorschau zeigt extrahierte Farben an
- Warnung wenn Hintergrundbild nicht verfügbar ist

## Implementierte Dateien

### 1. `WallpaperColorExtractor.kt`
**Hauptklasse für die Farbextraktion:**
- `extractBrightestColors()` - Extrahiert die 2 hellsten unterschiedlichen Farben
- `getColorPreview()` - Zeigt bis zu 6 dominante Farben für Vorschau
- `isWallpaperAccessible()` - Prüft ob Hintergrundbild verfügbar ist

**Algorithmus:**
1. Hintergrundbild vom WallpaperManager abrufen
2. Auf 50x50 Pixel skalieren für Performance
3. Farbquantisierung (32-Stufen pro Kanal)
4. Dominante Farben nach Häufigkeit sortieren
5. Hellste Farben mit ausreichendem Unterschied auswählen

### 2. `PreferencesManager.kt` (erweitert)
**Neue Einstellung hinzugefügt:**
- `AUTO_WALLPAPER_COLORS` - Boolean-Preference für automatische Farbanpassung
- `autoWallpaperColorsFlow` - StateFlow für UI-Binding
- `setAutoWallpaperColors()` - Setter-Funktion

### 3. `MainViewModel.kt` (erweitert)
**Neue Funktionen:**
- `autoWallpaperColors` - StateFlow für UI-Status
- `setAutoWallpaperColors()` - Aktiviert/deaktiviert automatische Farbanpassung
- `extractAndApplyWallpaperColors()` - Führt Extraktion durch und wendet Farben an
- `refreshWallpaperColors()` - Manuelle Aktualisierung der Farben
- `isWallpaperAccessible()` - Prüft Verfügbarkeit des Hintergrundbilds

### 4. `SettingsScreen.kt` (erweitert)
**Neue UI-Komponente:**
- `WallpaperColorSetting` - Composable für die Einstellungen
- Schalter zum Ein-/Ausschalten
- "Farben aktualisieren" Button
- Farbvorschau mit extrahierten Farben
- Statusanzeige für Hintergrundbild-Verfügbarkeit

## Verwendung

### Aktivierung
1. Öffne die Einstellungen der DetoxLauncher App
2. Gehe zum Bereich "Farben"
3. Aktiviere "Automatische Farbanpassung"
4. Die Farben werden sofort aus dem aktuellen Hintergrundbild extrahiert

### Manuelle Aktualisierung
- Klicke auf "Farben aktualisieren" um die Farben neu zu extrahieren
- Nützlich nach Änderung des Hintergrundbilds

### Farbvorschau
- Klicke auf "Vorschau anzeigen" um die extrahierten Farben zu sehen
- Zeigt bis zu 6 dominante Farben aus dem Hintergrundbild

## Technische Details

### Farbextraktion-Algorithmus
```kotlin
// 1. Farbquantisierung (reduziert ähnliche Farben)
val quantizedColor = quantizeColor(pixel)

// 2. Helligkeitsberechnung (wahrgenommene Luminanz)
val brightness = 0.299f * red + 0.587f * green + 0.114f * blue

// 3. Farbabstand im RGB-Raum
val distance = sqrt(deltaR² + deltaG² + deltaB²)
```

### Konfigurierbare Parameter
- `SAMPLE_SIZE = 50` - Bildgröße für Verarbeitung
- `MIN_COLOR_DIFFERENCE = 0.3f` - Mindestabstand zwischen Farben
- `MIN_BRIGHTNESS = 0.2f` - Mindesthelligkeit für Farbauswahl

### Performance
- Bildverarbeitung: ~10-50ms (je nach Gerät)
- Speicherverbrauch: Minimal durch Bildskalierung
- Hintergrundverarbeitung: Keine UI-Blockierung

## Vorteile

1. **Keine Berechtigungen**: Nutzt Standard-Android-APIs
2. **Automatisch**: Farben passen sich automatisch an das Hintergrundbild an
3. **Performance**: Optimiert für schnelle Verarbeitung
4. **Benutzerfreundlich**: Einfache Ein-/Ausschaltung
5. **Visuell ansprechend**: Wählt die hellsten und kontrastreichsten Farben
6. **Robust**: Fehlerbehandlung für nicht verfügbare Hintergrundbilder

## Mögliche Erweiterungen

1. **Farbharmonie**: Komplementär- oder Analogfarben generieren
2. **Zeitbasiert**: Verschiedene Farben je nach Tageszeit
3. **Benutzerfilter**: Ausschluss bestimmter Farbbereiche
4. **Live-Updates**: Automatische Aktualisierung bei Hintergrundbild-Änderung
5. **Farbpaletten**: Speichern und Wiederverwenden von Farbkombinationen

## Kompatibilität

- **Android Version**: API 30+ (entspricht der App-Mindestversion)
- **Geräte**: Alle Android-Geräte mit Standard-Hintergrundbild-Support
- **Performance**: Optimiert für alle Geräteklassen
