package com.kevinnovation.detoxlauncher.utils

import android.app.WallpaperManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 * Utility class for extracting colors from wallpaper
 * Extracts the two brightest distinct colors from the current wallpaper
 */
object WallpaperColorExtractor {
    
    private const val TAG = "WallpaperColorExtractor"
    private const val SAMPLE_SIZE = 50 // Reduce image size for faster processing
    private const val MIN_COLOR_DIFFERENCE = 0.3f // Minimum difference between colors
    private const val MIN_BRIGHTNESS = 0.2f // Minimum brightness threshold
    
    /**
     * Extract the two brightest distinct colors from the current wallpaper
     * Returns null if wallpaper cannot be accessed or no suitable colors found
     */
    suspend fun extractBrightestColors(context: Context): Pair<Color, Color>? {
        return withContext(Dispatchers.Default) {
            try {
                val wallpaperManager = WallpaperManager.getInstance(context)
                val wallpaperDrawable = wallpaperManager.drawable
                
                if (wallpaperDrawable == null) {
                    Log.w(TAG, "No wallpaper drawable available")
                    return@withContext null
                }
                
                val bitmap = drawableToBitmap(wallpaperDrawable)
                val scaledBitmap = scaleBitmap(bitmap, SAMPLE_SIZE)
                
                val dominantColors = extractDominantColors(scaledBitmap)
                val brightestColors = findBrightestDistinctColors(dominantColors)
                
                bitmap.recycle()
                scaledBitmap.recycle()
                
                brightestColors
            } catch (e: Exception) {
                Log.e(TAG, "Error extracting wallpaper colors", e)
                null
            }
        }
    }
    
    /**
     * Convert drawable to bitmap
     */
    private fun drawableToBitmap(drawable: Drawable): Bitmap {
        if (drawable is BitmapDrawable && drawable.bitmap != null) {
            return drawable.bitmap
        }
        
        val bitmap = Bitmap.createBitmap(
            drawable.intrinsicWidth.takeIf { it > 0 } ?: 1,
            drawable.intrinsicHeight.takeIf { it > 0 } ?: 1,
            Bitmap.Config.ARGB_8888
        )
        
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)
        
        return bitmap
    }
    
    /**
     * Scale bitmap to reduce processing time
     */
    private fun scaleBitmap(bitmap: Bitmap, maxSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }
        
        val scale = min(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    /**
     * Extract dominant colors from bitmap using color quantization
     */
    private fun extractDominantColors(bitmap: Bitmap): List<ColorInfo> {
        val colorMap = mutableMapOf<Int, Int>()
        val width = bitmap.width
        val height = bitmap.height
        
        // Sample pixels (skip some for performance)
        val step = max(1, min(width, height) / 20)
        
        for (y in 0 until height step step) {
            for (x in 0 until width step step) {
                val pixel = bitmap.getPixel(x, y)
                val quantizedColor = quantizeColor(pixel)
                colorMap[quantizedColor] = colorMap.getOrDefault(quantizedColor, 0) + 1
            }
        }
        
        return colorMap.map { (color, count) ->
            ColorInfo(Color(color), count, calculateBrightness(Color(color)))
        }.sortedByDescending { it.count }
    }
    
    /**
     * Quantize color to reduce similar colors
     */
    private fun quantizeColor(color: Int): Int {
        val factor = 32 // Reduce color space
        val r = ((color shr 16 and 0xFF) / factor) * factor
        val g = ((color shr 8 and 0xFF) / factor) * factor
        val b = ((color and 0xFF) / factor) * factor
        val a = color shr 24 and 0xFF
        
        return (a shl 24) or (r shl 16) or (g shl 8) or b
    }
    
    /**
     * Find the two brightest distinct colors
     */
    private fun findBrightestDistinctColors(colors: List<ColorInfo>): Pair<Color, Color>? {
        val brightColors = colors.filter { it.brightness >= MIN_BRIGHTNESS }
        
        if (brightColors.size < 2) {
            Log.w(TAG, "Not enough bright colors found")
            return null
        }
        
        val firstColor = brightColors.first()
        
        // Find second color that is sufficiently different
        val secondColor = brightColors.drop(1).find { color ->
            calculateColorDistance(firstColor.color, color.color) >= MIN_COLOR_DIFFERENCE
        }
        
        if (secondColor == null) {
            Log.w(TAG, "No sufficiently distinct second color found")
            return null
        }
        
        return Pair(firstColor.color, secondColor.color)
    }
    
    /**
     * Calculate brightness of a color (perceived luminance)
     */
    private fun calculateBrightness(color: Color): Float {
        // Use perceived luminance formula
        return 0.299f * color.red + 0.587f * color.green + 0.114f * color.blue
    }
    
    /**
     * Calculate distance between two colors in RGB space
     */
    private fun calculateColorDistance(color1: Color, color2: Color): Float {
        val deltaR = color1.red - color2.red
        val deltaG = color1.green - color2.green
        val deltaB = color1.blue - color2.blue
        
        return sqrt(deltaR * deltaR + deltaG * deltaG + deltaB * deltaB)
    }
    
    /**
     * Data class to hold color information
     */
    private data class ColorInfo(
        val color: Color,
        val count: Int,
        val brightness: Float
    )
    
    /**
     * Check if wallpaper access is available (no special permissions needed for reading wallpaper)
     */
    fun isWallpaperAccessible(context: Context): Boolean {
        return try {
            val wallpaperManager = WallpaperManager.getInstance(context)
            wallpaperManager.drawable != null
        } catch (e: Exception) {
            Log.e(TAG, "Error checking wallpaper accessibility", e)
            false
        }
    }
    
    /**
     * Get a preview of extracted colors for testing
     */
    suspend fun getColorPreview(context: Context): List<Color> {
        return withContext(Dispatchers.Default) {
            try {
                val wallpaperManager = WallpaperManager.getInstance(context)
                val wallpaperDrawable = wallpaperManager.drawable ?: return@withContext emptyList()
                
                val bitmap = drawableToBitmap(wallpaperDrawable)
                val scaledBitmap = scaleBitmap(bitmap, SAMPLE_SIZE)
                val dominantColors = extractDominantColors(scaledBitmap)
                
                bitmap.recycle()
                scaledBitmap.recycle()
                
                dominantColors.take(6).map { it.color }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting color preview", e)
                emptyList()
            }
        }
    }
}
